'use client';

import Image from 'next/image';
import { useState } from 'react';
import { motion, Variants } from 'framer-motion';
import Link from 'next/link';
import EmailInput from './ui/EmailInput';
import PrimaryBtn from './ui/PrimaryBtn';

const computerVariants: Variants = {
  offscreen: {
    y: -30,
  },
  onscreen: {
    y: -120,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 20
    },
  },
};

export default function NewsletterSubscription() {
  const [email, setEmail] = useState('');
  const [invalidEmail, setInvalidEmail] = useState('');

  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email)
  }

  const handleSubscribe = async () => {
    setInvalidEmail('');
    
    if (!validateEmail(email)) {
      setInvalidEmail('Please enter a valid email');
      return;
    }

    // TODO: Handle API call. Example below:
    // try {
    //   const response = await fetch('api/newsletter/subscribe', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify({ email })
    //   });

    //   if (!response.ok) throw new Error('Subscription failed');

    //   // Email is valid at this point
    //   setEmail('');
    // } catch (err) {
    //   setError('Failed to subscribe. Please try again.')
    // }
  };

  return (
    <>
      {/* extra spacing because computer image reaches above its container */}
      <div className="h-20"></div>

      {/* Why isn't secondary-50 working? */}
      <div className="w-full bg-secondary-50 py-20">

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center max-w-[1200px] mx-auto">
            {/* Left Content - Computer Screen */}
            <motion.div 
              className="relative w-[500px] h-[375px] mx-auto lg:mx-0"
              initial="offscreen"
              whileInView="onscreen"
              viewport={{ amount: 0.6, once: true }}
              variants={computerVariants}
            >
              <div className="relative w-full h-full">
                {/* Computer Screen Base Image */}
                <Image
                  src="/images/blank-computer-screen.png"
                  alt="Computer screen"
                  fill
                  className="object-contain"
                  priority
                />
                {/* Stay Connected Image - Positioned inside the screen */}
                <div className="absolute top-[2%] left-[3%] right-[3%] bottom-[27%]">
                  <Image
                    src="/images/stay-connected.png"
                    alt="Stay Connected"
                    fill
                    className="object-cover rounded-sm"
                    priority
                  />
                </div>
              </div>
            </motion.div>

            {/* Right Content - Newsletter Form */}
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-4xl md:text-5xl font-semibold font-['Poppins'] text-gray-900 leading-tight">
                  Stay updated with our newsletter
                </h2>
                <p className="text-gray-600 font-['Poppins']">
                  Subscribe to receive the latest updates, insights, and resources directly to your inbox.
                </p>
              </div>

              <div className="space-y-4">
                <EmailInput
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  invalidEmail={invalidEmail}
                />
                <div className="space-y-3">
                  <PrimaryBtn text="Subscribe" onClick={handleSubscribe} />
                  <p className="text-xs text-gray-500 font-['Poppins']">
                    By clicking Subscribe, you agree to our <Link href="/terms" className="underline">Terms and Conditions</Link>.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
