"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import PrimaryBtn from "./PrimaryBtn";

export interface Slide {
  title: string;
  description: string;
  imagePath: string;
  buttonText: string;
  buttonLink?: string;
}

interface SlideshowBannerProps {
  slides: Slide[];
  autoSlideInterval?: number;
}

export default function SlideshowBanner({ slides, autoSlideInterval = 8000 }: SlideshowBannerProps) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [recentlyTouched, setRecentlyTouched] = useState(false);
  const [isTabVisible, setIsTabVisible] = useState(true);
  const [isAnimating, setIsAnimating ] = useState(false);

  // Handle tab visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsTabVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);
  
  useEffect(() => {
    // Don't start the interval if recently touched or tab is not visible
    if (recentlyTouched || !isTabVisible) return;

    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, autoSlideInterval);

    // Cleanup interval on unmount or when recentlyTouched changes
    return () => clearInterval(timer);
  }, [recentlyTouched, isTabVisible]);

  const handleInteraction = () => {
    setRecentlyTouched(true);

    setTimeout(() => {
      setRecentlyTouched(false);
    }, 9000);
  };

  const handleButtonClick = () => {
    handleInteraction();
    //TODO: Route to the appropriate page
  };

  const handleSlideChange = (index: number) => {
    if (isAnimating) return;

    setIsAnimating(true);
    setCurrentSlide(index);
    handleInteraction();
  };

  // Keyboard navigation (left/right arrows for slideshow control).
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isAnimating) return;

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          handleInteraction();
          setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
          break;
        case 'ArrowRight':
          e.preventDefault();
          handleInteraction();
          setCurrentSlide((prev) => (prev + 1) % slides.length);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentSlide, isAnimating]);

  return (
    <div
      className="relative w-full h-[400px] md:h-[600px] lg:h-[812px] pb-8 flex flex-col justify-center items-center gap-6 overflow-hidden"
      role="region"
      aria-label="Image Slideshow"
    >
      {/* Slideshow Content */}
      <div 
        className="w-full h-full flex justify-start items-center relative overflow-hidden"
      >
        <AnimatePresence
          mode="wait"
          onExitComplete={() => setIsAnimating(false)}
        >
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="absolute inset-0 w-full h-full"
            role="group"
            aria-roledescription="slide"
            aria-label={`Slide ${currentSlide + 1} of ${slides.length}`}
          >
            <div className="w-full h-full relative bg-gray-900/40 rounded-xl overflow-hidden">
              {/* Image Container */}
              <div className="absolute inset-0 w-full h-full">
                <Image
                  src={slides[currentSlide]?.imagePath}
                  alt={slides[currentSlide]?.title}
                  fill
                  priority={currentSlide === 0} // Only prioritize first slide
                  className="object-cover object-center"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
                  quality={90}
                />
                {/* IDEA #1 - Solid overlay */}
                {/* <div className="absolute inset-0 bg-black/50" /> */}

                {/* IDEA #2 - Gradient overlay*/}
                <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-black/50 to-transparent" />
              </div>

              <motion.div 
                className="relative z-10 w-full h-full px-8 md:px-32 py-12 md:py-24 flex flex-col justify-center items-start gap-8 md:gap-6"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <div className="flex flex-col justify-start items-start gap-4 md:gap-6 text-left">
                  <motion.h2 
                    className="max-w-[1000px] text-3xl md:text-5xl lg:text-6xl text-gray-50 font-semibold font-['Poppins'] leading-tight md:leading-[75px]"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    {slides[currentSlide]?.title}
                  </motion.h2>
                  <motion.p 
                    className="max-w-[550px] text-left text-gray-300 text-base md:text-lg font-normal font-['Poppins'] leading-7"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    {slides[currentSlide]?.description}
                  </motion.p>
                </div>
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{
                    y: { delay: 0.5 }, // Only delay the initial appearance animation
                    opacity: { delay: 0.5 }, // Only delay the initial appearance animation
                    scale: { duration: 0.2 } // Quick response for hover/tap scaling
                  }}
                >
                  <PrimaryBtn
                    text={slides[currentSlide]?.buttonText ?? "Learn More"}
                    link={slides[currentSlide]?.buttonLink ?? "#"}
                    onClick={handleButtonClick}
                    onMouseEnter={handleInteraction}
                    onMouseLeave={() => setRecentlyTouched(false)}
                  />
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation Dots */}
      <div
        className="relative z-10 inline-flex justify-center items-center gap-3"
        role="group"
        aria-label="Slideshow navigation"
      >
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => handleSlideChange(index)}
            className={`w-2 h-2 md:w-3 md:h-3 rounded-full ${
              currentSlide === index ? "bg-violet-900" : "bg-gray-400"
              }`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
            animate={{
              scale: currentSlide === index ? 1.2 : 1,
              backgroundColor: currentSlide === index ? "#FFFFFF" : "#9CA3AF"
            }}
            transition={{ duration: 0.2 }}
            aria-label={`Go to slide ${index + 1}`}
            aria-current={currentSlide === index ? "true" : "false"}
          />
        ))}
      </div>

      <div className="sr-only">
        Use left and right arrow keys to navigate between slides
      </div>
    </div>
  );
}
