// Zod-based validation
"use client"
import { Button } from '@heroui/button';
import Image from "next/image";
import { useRouter } from "next/navigation";
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import { useDictionary } from '@/lib/DictionaryContext';
import { z } from 'zod';

const ContactEnum = z.enum(["email", "phone"]);

const PrayerRequestSchema = z.object({
  fullname: z.string().min(1, "Full name is required"),
  preferredContact: ContactEnum,
  email: z.string().email("Invalid email").optional(),
  phoneNumber: z.string().min(10, "Phone number is required").optional(),
  textArea: z.string().min(1, "Prayer text is required"),
}).refine((data) => {
  return (
    (data.preferredContact === "email" && data.email) ||
    (data.preferredContact === "phone" && data.phoneNumber)
  );
}, {
  message: "Please provide a valid contact method",
  path: ["preferredContact"],
});

enum Contact {
  Email = "email",
  Phone = "phone",
}

const PrayerRequestCard = function PrayerRequestCard() {
  const dictionary = useDictionary();
  const router = useRouter();

  const [open, setOpen] = useState(false);
  const [openForm, setOpenForm] = useState(true);
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  const [hoveredCard, setHoveredCard] = useState<0 | 1>(0);
  const [preferredContact, setPreferredContact] = useState<Contact>(Contact.Email);

  const [fullname, setFullname] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [textArea, setTextArea] = useState("");
  const [submitPressed, setSubmitPressed] = useState(false);

  const [validFullname, setValidFullname] = useState(true);
  const [validPhoneNumber, setValidPhoneNumber] = useState(true);
  const [validEmail, setValidEmail] = useState(true);
  const [validTextArea, setValidTextArea] = useState(true);

  const handleOpen = () => {
    setOpenForm(true);
    setOpen(true);
    setFieldsToDefault();
    setSubmitPressed(false);
    setFieldsToInvalid();
    setPreferredContact(Contact.Email);
  };

  const handleClose = () => {
    setOpen(false);
    setOpenSuccessModal(false);
    setFieldsToDefault();
    setSubmitPressed(false);
    setFieldsToInvalid();
    setPreferredContact(Contact.Email);
  };

  const setFieldsToDefault = () => {
    setFullname("");
    setPhoneNumber("");
    setEmail("");
    setTextArea("");
  };

  const setFieldsToInvalid = () => {
    setValidFullname(true);
    setValidPhoneNumber(true);
    setValidEmail(true);
    setValidTextArea(true);
  };

  const handlePreferredContact = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setPreferredContact(e.target.value as Contact);
    setPhoneNumber("");
    setEmail("");
  };

  const attemptSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitPressed(true);

    const result = PrayerRequestSchema.safeParse({
      fullname,
      preferredContact,
      email,
      phoneNumber,
      textArea,
    });

    if (result.success) {
      handleSubmit();
      setSubmitPressed(false);
    } else {
      const errors = result.error.flatten().fieldErrors;
      setValidFullname(!errors.fullname);
      setValidPhoneNumber(!errors.phoneNumber);
      setValidEmail(!errors.email);
      setValidTextArea(!errors.textArea);
    }
  };

  const handleSubmit = () => {
    setOpenSuccessModal(true);
    setOpenForm(false);
    setHoveredCard(0);
    setFieldsToDefault();
    setPreferredContact(Contact.Email);
  };

  // Keep other methods and UI structure unchanged
  // e.g., handleTalkToSomeone, handleReceiveEncouragement, render logic...

  return (
    <div> {/* Keep your existing JSX structure */} </div>
  );
};

export default PrayerRequestCard;
