export interface BibleVerse {
  book: string; // Name of the book (e.g., "Genesis", "Matthew")
  chapter: number; // Chapter number
  verse: number; // Verse number
  text: string; // The actual text of the verse
}

export const popularVerses: BibleVerse[] = [
  {
    book: "<PERSON>",
    chapter: 3,
    verse: 16,
    text: "For God so loved the world that He gave His only begotten Son, that whoever believes in Him should not perish but have everlasting life.",
  },
  {
    book: "Psalm",
    chapter: 23,
    verse: 1,
    text: "The Lord is my shepherd; I shall not want.",
  },
  {
    book: "Romans",
    chapter: 8,
    verse: 28,
    text: "And we know that all things work together for good to those who love God, to those who are called according to His purpose.",
  },
  {
    book: "Philippians",
    chapter: 4,
    verse: 13,
    text: "I can do all things through Christ who strengthens me.",
  },
  {
    book: "Genesis",
    chapter: 1,
    verse: 1,
    text: "In the beginning God created the heavens and the earth.",
  },
];
