'use client'

import { But<PERSON> } from '@heroui/button';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import ListenBtn from './ui/ListenBtn';
import { ChevronDown, ChevronUp, Search } from 'lucide-react';
import { Tooltip } from '@heroui/tooltip';

export default function Navbar() {
  const [isFaithFormationDropdownOpen, setIsFaithFormationDropdownOpen] = useState(false);
  const [isExploreDropdownOpen, setIsExploreDropdownOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  return (
    <nav className='bg-white'>
      <div className='flex gap-6 justify-between items-center p-4'>
        <div className='flex-1 flex items-center gap-6'>
            <div>
            <Link href="/">
              <Image src="/icons/fish-logo.svg" alt="logo" width={70} height={32} />
            </Link>
            </div>
          <div className='flex-1 flex items-center justify-between'>
            <div className='flex items-center gap-6'>
              <Tooltip content={<FaithFormationDropdown />} delay={500} isOpen={isFaithFormationDropdownOpen} onOpenChange={(open) => setIsFaithFormationDropdownOpen(open)}>
                <Button variant="bordered" className='p-0'>
                  <div className='flex items-center gap-1'>
                    <Link href="/faith-formation" className="font-semibold text-[#571B93]">Faith Formation</Link>
                    {isFaithFormationDropdownOpen
                      ? <ChevronUp size={18} />
                      : <ChevronDown size={18} />
                    }
                  </div>
                </Button>
              </Tooltip>
              <Tooltip content={<ExploreDropdown />} delay={500} isOpen={isExploreDropdownOpen} onOpenChange={(open) => setIsExploreDropdownOpen(open)} placement='bottom-start'>
                <Button variant="bordered" className='p-0'>
                  <div className='flex items-center gap-1'>
                    <span>Explore</span>
                    {isExploreDropdownOpen
                      ? <ChevronUp size={18} />
                      : <ChevronDown size={18} />
                    }
                  </div>
                </Button>
              </Tooltip>
              <Link href="#">Friend of Faith</Link>
              <Link href="#">Contact</Link>
            </div>
            <div className='flex items-center'>
              {isSearchOpen
                ? <div className='p-1.5 px-4 border-1 border-gray-300 rounded-full flex gap-1 items-center'>
                  <Search size={16} className='text-gray-400' />
                  <input type="search" placeholder='Search website name...' className='border-0 outline-0 bg-transparent placeholder:text-sm' />
                </div>
                : <Button isIconOnly onPress={() => setIsSearchOpen(true)}>
                  <Image src="/icons/search-icon-black.svg" alt="logo" width={16} height={16} />
                </Button>
              }
            </div>
          </div>
        </div>
        <ListenBtn />
      </div>
    </nav>
  );
}

function FaithFormationDropdown() {
  return (
    <DropdownWrapper>
      <DropdownItem
        title='Foundations of Christianity'
        description='What does it take to start a relationship with God? Is it about committing to selfless acts or striving to become a better person to gain His acceptance?'
      />
      <DropdownItem
        title='Good News'
        description='From the very beginning, the Bible—both the Old and New Testaments—tells the story of God&apos;s love, redemption, and plan to restore humanity.'
      />
      <DropdownItem
        title='Meet Jesus'
        description='Understanding what it means to have faith in Christ and how to build a relationship with Him.'
      />
      <DropdownItem
        title='Navigating Life&apos;s Challenges'
        description='Practical insights and guidance to help you move forward through difficult situations.'
      />
      <DropdownItem
        title='Know God'
        description='Would you like to know God personally? The following truths can help you understand how to begin a personal relationship with God through Jesus Christ.'
      />
      <DropdownItem
        title='Salvation'
        description='Why do you need salvation? Have you put your faith in Jesus Christ?'
      />
    </DropdownWrapper>
  )
}

function ExploreDropdown() {
  return (
    <DropdownWrapper>
      <DropdownItem
        title='Blog'
        description='Up-to-date articles posted daily to keep our community connected.'
      />
      <DropdownItem
        title='Resources'
        description='Videos, articles, and more to enrich your daily life.'
      />
      <DropdownItem
        title='Daily Prayers'
        description='Follow daily prayers to nurture your relationship with Christ.'
      />
      <DropdownItem
        title='Bible Study'
        description='Exploration guides to help unpack the deeper meaning of Scripture.'
      />
      <DropdownItem
        title='Stories of Transformation'
        description='Discover stories of people around the world who have experienced Christ.'
      />
      <DropdownItem
        title='Faith FAQs'
        description='Have a different question and can&apos;t seem to find the answer you&apos;re looking for? Check out our Faith FAQs. '
      />
    </DropdownWrapper>
  )
}

function DropdownWrapper({ children }: { children: React.ReactNode }) {
  return (
    <div className='left-0 py-6 px-4 pl-[96px] m-0 rounded-bl-md rounded-br-md bg-white'>
      <div className='lg:w-7/12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 grid-rows-2 gap-x-12 gap-y-6'>
        {children}
      </div>
    </div>
  )
}

function DropdownItem({ title, description }: { title: string, description: string }) {
  return (
    <div className='flex flex-col gap-2'>
      <Link href="#" className='font-semibold text-[#571B93]'>{title}</Link>
      <p className='text-[12px]'>
        {description}
      </p>
    </div>
  )
}
