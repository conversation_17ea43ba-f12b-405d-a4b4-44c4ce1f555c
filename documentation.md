--- feature-caleb
## 🖼️ Frontend Components
PrayerRequestCard
DisipleshipCard

### 1. Component Name & Purpose
- PrayerRequestCard
- Invites the user to submit a prayer request or contact someone.

### 2. State Management (if applicable)
- Local `useState` is used to manage the open/close state of the modal that contains the prayer request form. Also used field and button states etc.

### 3. Dependencies
- `@heroui/button` is used for most buttons.
- `next/image` is used to display the image.
- `React` is used for `useState` to manage state locally.
- `framer-motion` is used to animate the modal and for interactive buttons used after pressing submit on the modal.
- `next/navigation` is used for routing navigation for example to the "/talk-to-someone" button.
- `../content/PrayerRequestContent` used to import the text, placeholders, button text and image paths.`
- `validator` used to validate the input for email and phone number in a contact form.
- `useDictionary` to import content from our dictionary.
### 4. Styling Notes
- Using vh to make the modal dynamically responsive but using the default provided sizing from mockup as the default max sizing.
- Stacking components when size decreases such that from left to right, the left-most component takes priority in stacking ontop of the other right-most components.
- Scrolling overflow allows for usability on the modal or page when the screen is too small vertically.

### 1. Component Name & Purpose
- DisipleshipCard
- Invites the user to subscribe to a newsletter.

### 2. State Management (if applicable)
- Local `useState` is used to manage the open/close state of the modal that contains the prayer request form. Also used field and button states etc.
### 3. Dependencies
- `@heroui/button` is used for most buttons.
- `next/image` is used to display the image.
- `React` is used for `useState` to manage state locally.
- `framer-motion` is used to animate the modal and for interactive buttons used after pressing submit on the modal.
- `next/navigation` is used for routing navigation for example to the "/talk-to-someone" button.
- `../content/PrayerRequestContent` used to import the text, placeholders, button text and image paths.`
- `validator` used to validate the input for email and phone number in a contact form.
- `useDictionary` to import content from our dictionary.


### 4. Styling Notes
- Reusing much of the code from the PrayerRequestCard to easily make the components look consistent and similar.
- Stacking components when size decreases such that from left to right, the left-most component takes priority in stacking ontop of the other right-most components.

---

--

# Documentation Guidelines

To ensure clarity, maintainability, and ease of onboarding, please follow these guidelines when documenting your work.

## 🖼️ Frontend Components

For each frontend component, include the following:

### 1. Component Name & Purpose

Briefly describe what the component does.

### 2. State Management (if applicable)

Mention any local or global state the component uses.

### 3. Dependencies

Mention any third-party libraries or custom hooks used.

### 4. Styling Notes

Briefly explain any special styling approaches (e.g., Tailwind, CSS modules).

## 🧠 Backend Modules / Functions / Classes

For each backend component, document the following:

### 1. Name & Purpose

Explain what the function/module/class is responsible for.

### 2. Parameters and Return Types

List the input parameters and the expected output, including types.

### 3. Expected Input/Output Examples

Show example data where possible.

### 4. Dependencies

Mention any external libraries, services, or database interactions.

### 5. Usage Example

Demonstrate how the function/class is typically called or used.

### 6. Special Notes

Include any environment variables, error handling logic, or edge cases to be aware of.
---Development
