@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import "swiper/css";
@import "swiper/css/pagination";
@tailwind base;
@tailwind components;
@tailwind utilities;
/* @import "tailwindcss"; */

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #571b93;
  --text-900: #121827;
}

/* Removed invalid @theme rule. If needed, define custom properties directly in :root or another selector. */

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--text-900);
}

.vod-img {
  background-image: url("../../public/images/verse-pic.png");
}
