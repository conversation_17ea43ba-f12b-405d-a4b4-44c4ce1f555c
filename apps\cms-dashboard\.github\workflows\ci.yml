name: CMS Dashboard CI

on:
  push:
    paths:
      - 'apps/cms-dashboard/**'
      - '.github/workflows/cms-dashboard-ci.yml'
  pull_request:
    paths:
      - 'apps/cms-dashboard/**'
      - '.github/workflows/cms-dashboard-ci.yml'

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci
        working-directory: ./apps/cms-dashboard

      - name: Lint
        run: npm run lint
        working-directory: ./apps/cms-dashboard

      - name: Type check
        run: npm run type-check
        working-directory: ./apps/cms-dashboard

      - name: Build
        run: npm run build
        working-directory: ./apps/cms-dashboard

      - name: Test
        run: npm run test
        working-directory: ./apps/cms-dashboard 