"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { Play } from "lucide-react";
import { useState } from "react";
import ReactPlayer from "react-player";
import PrimaryBtn from "./ui/PrimaryBtn";
import SecondaryBtn from "./ui/SecondaryBtn";

export default function StartFaithJourney() {
  const [isPlaying, setIsPlaying] = useState(false);

  return (
    <div className="w-full bg-white">
      <div className="self-stretch px-4 sm:px-20 lg:px-28 py-24 inline-flex flex-col justify-start items-start gap-16">
        {/* Top section with text and video */}
        <motion.div 
          className="self-stretch flex flex-col lg:flex-row justify-start items-center gap-12 overflow-hidden"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          {/* Left Content - Text */}
          <motion.div 
            className="w-full lg:w-[480px] rounded-[20px] flex flex-col justify-start items-start gap-12"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="self-stretch flex flex-col justify-start items-start gap-3">
              <div className="self-stretch flex flex-col justify-start items-start gap-1">
                <h2 className="self-stretch justify-start text-gray-900 text-3xl md:text-5xl font-semibold font-['Poppins'] leading-tight md:leading-[60px]">
                  Start your faith journey
                </h2>
              </div>
              <p className="w-full max-w-[600px] justify-center text-gray-500 text-base font-normal font-['Poppins'] leading-normal">
                Isaalmasih serves as a vital resource for people like you. Engage with meaningful content as we grow together on this new walk of life.
              </p>
            </div>
            <div className="inline-flex justify-start items-center gap-6">
              <PrimaryBtn text="Learn more" />
            </div>
          </motion.div>

          {/* Right Content - Video - now shown on all screen sizes but positioned differently */}
          <motion.div 
            className="w-full lg:flex-1 self-stretch px-4 sm:px-12 lg:px-28 py-24 lg:py-52 bg-black/20 rounded-xl flex flex-col justify-center items-center gap-2.5 overflow-hidden mt-12 lg:mt-0"
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="relative w-full h-full rounded-xl overflow-hidden">
              {!isPlaying ? (
                <>
                  <Image
                    src="/images/community-preview.jpg"
                    alt="Community members celebrating together"
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <motion.button
                      className="w-20 h-20 bg-white/70 rounded-full flex items-center justify-center"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsPlaying(true)}
                    >
                      <Play size={32} className="text-gray-900 ml-1" />
                    </motion.button>
                  </div>
                </>
              ) : (
                <ReactPlayer
                  url="/videos/community-video.mp4"
                  width="100%"
                  height="100%"
                  playing
                  controls
                  onEnded={() => setIsPlaying(false)}
                />
              )}
            </div>
          </motion.div>
        </motion.div>

        {/* Bottom section with cards */}
        <motion.div 
          className="self-stretch inline-flex flex-col md:flex-row justify-start items-start gap-6 md:gap-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {/* Card 1 */}
          <div 
            className="flex-1 self-stretch rounded-md p-6 inline-flex flex-col justify-between items-start overflow-hidden"
          >
            <div className="self-stretch flex flex-col justify-start items-start gap-6">
              <div className="p-3 bg-[#f2eafa] rounded-lg outline outline-2 outline-offset-[-2px] outline-[#e4d4f4] inline-flex justify-start items-center gap-2.5">
                <div data-size="24" className="w-6 h-6 relative overflow-hidden">
                    <Image 
                      src="/icons/hand-holding-droplet.svg"
                      alt="Who is Jesus?"
                      width={40}
                      height={40}
                    />
                </div>
              </div>

              <div className="self-stretch flex flex-col justify-start items-start gap-3">
                <h3 className="self-stretch justify-start text-gray-900 text-lg font-semibold font-['Poppins'] leading-7">
                  Who is Jesus?
                </h3>
                <p className="self-stretch justify-start text-gray-600 text-sm font-normal font-['Poppins'] leading-tight">
                  Read truths to uncover who Jesus is and why He loves you.
                </p>
              </div>
            </div>
            <div className="inline-flex justify-start items-start mt-6">
              <SecondaryBtn text="Meet Jesus" />
            </div>
          </div>

          {/* Card 2 */}
          <div 
            className="flex-1 self-stretch rounded-md p-6 inline-flex flex-col justify-between items-start overflow-hidden"
          >
            <div className="self-stretch flex flex-col justify-start items-start gap-6">
              <div className="p-3 bg-[#f2eafa] rounded-lg outline outline-2 outline-offset-[-2px] outline-[#e4d4f4] inline-flex justify-start items-center gap-2.5">
                <div data-size="24" className="w-6 h-6 relative overflow-hidden">
                    <Image 
                      src="/icons/book-open-reader.svg"
                      alt="Stories of Faith"
                      width={40}
                      height={40}
                    />
                </div>
              </div>

              <div className="self-stretch flex flex-col justify-start items-start gap-3">
                <h3 className="self-stretch justify-start text-gray-900 text-lg font-semibold font-['Poppins'] leading-7">
                  Stories of Faith
                </h3>
                <p className="self-stretch justify-start text-gray-600 text-sm font-normal font-['Poppins'] leading-tight">
                  Testimonials of believers within our community.
                </p>
              </div>
            </div>
            <div className="inline-flex justify-start items-start mt-6">
              <SecondaryBtn text="Read stories" />
            </div>
          </div>

          {/* Card 3 */}
          <div 
            className="flex-1 self-stretch rounded-md p-6 inline-flex flex-col justify-between items-start overflow-hidden"
          >
            <div className="self-stretch flex flex-col justify-start items-start gap-6">
              <div className="p-3 bg-[#f2eafa] rounded-lg outline outline-2 outline-offset-[-2px] outline-[#e4d4f4] inline-flex justify-start items-center gap-2.5">
                <div data-size="24" className="w-6 h-6 relative overflow-hidden">
                    <Image 
                      src="/icons/users-solid.svg"
                      alt="Find Your People"
                      width={40}
                      height={40}
                    />
                </div>
              </div>

              <div className="self-stretch flex flex-col justify-start items-start gap-3">
                <h3 className="self-stretch justify-start text-gray-900 text-lg font-semibold font-['Poppins'] leading-7">
                  Find Your People
                </h3>
                <p className="self-stretch justify-start text-gray-600 text-sm font-normal font-['Poppins'] leading-tight">
                  Faith is not a solo journey, but about belonging.
                </p>
              </div>
            </div>
            <div className="inline-flex justify-start items-start mt-6">
              <SecondaryBtn text="Browse discipleship resources" />
            </div>
          </div>

          {/* Card 4 */}
          <div 
            className="flex-1 self-stretch rounded-md p-6 inline-flex flex-col justify-between items-start overflow-hidden"
          >
            <div className="self-stretch flex flex-col justify-start items-start gap-6">

              <div className="p-3 bg-[#f2eafa] rounded-lg outline outline-2 outline-offset-[-2px] outline-[#e4d4f4] inline-flex justify-start items-center gap-2.5">
                <div data-size="24" className="w-6 h-6 relative overflow-hidden">
                  <Image 
                    src="/icons/briefcase.svg"
                    alt="Tools for Your Journey"
                    width={40}
                    height={40}
                  />
                </div>
              </div>

              <div className="self-stretch flex flex-col justify-start items-start gap-3">
                <h3 className="self-stretch justify-start text-gray-900 text-lg font-semibold font-['Poppins'] leading-7">
                  Tools for Your Journey
                </h3>
                <p className="self-stretch justify-start text-gray-500 text-sm font-normal font-['Poppins'] leading-tight">
                  Valuable resources tailored to your spiritual journey and community needs.
                </p>
              </div>
            </div>
            <div className="inline-flex justify-start items-start mt-6">
              <SecondaryBtn text="Explore now" />
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
