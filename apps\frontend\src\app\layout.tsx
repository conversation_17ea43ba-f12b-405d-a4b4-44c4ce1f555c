import { Metadata } from "next";
import React from "react";
import { Poppins } from "next/font/google";
import localFont from "next/font/local";
import "./globals.css";
import dictionary from "@/dictionary.json"
import { ContentProvider } from '@/lib/DictionaryContext';


const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});


export const metadata: Metadata = {
  title: "E-mission to Islam Website",
  description: "Spreading the Gospel to Muslims for Christ and spiritual growth through digital missions.",
  icons: {
    icon: "/icons/fish-logo.svg",
    shortcut: "/icons/fish-logo.svg",
    apple: "/icons/fish-logo.svg",
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${poppins.variable} ${geistMono.variable} antialiased`}
      >
        <ContentProvider content={dictionary}>{children}</ContentProvider>

      </body>
    </html>
  );
}
