import { Button } from '@heroui/button'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

export default function Footer() {
  return (
    <footer className='p-4 bg-[#F9F4FF]'>
      <div className='py-21 pt-12 flex justify-between items-center'>
        <div>
          <div className='text-4xl font-semibold'>Subscribe to `website name&apos;s` newsletter</div>
          <div>Get the latest news community new, sent to your inbox weekly.</div>
        </div>
        <div className='flex gap-2 flex-col items-start '>
          <div className='flex gap-4'>
            <input type="email" placeholder='Enter your email' className='bg-white border-1 border-gray-200 rounded placeholder:text-sm px-3 py-1.5' />
            <Button className='bg-[#571B93] text-white rounded'>Sign up</Button>
          </div>
          <p className='text-[12px]'>
            <span>By clicking “Sign up” you agree to our </span>
            <Link href="#" className='underline'>TOS</Link>
            <span> and </span>
            <Link href="#" className='underline'>Privacy Policy</Link>
          </p>
        </div>
      </div>
      <div className='flex justify-between gap-20'>
        <div className='w-3/12 flex flex-col justify-between'>
          <div>
            <Image src="/icons/fish-logo.svg" alt="logo" width={70} height={32} />
            <p className="font-semibold text-2xl">
              Making heaven more crowded, one soul at a time.
            </p>
          </div>
          <div className='flex gap-6'>
            <Link href="#"> <Image src="/icons/whatsapp-logo.svg" alt='whatsapp icon' width={24} height={24} /> </Link>
            <Link href="#"> <Image src="/icons/facebook-logo.svg" alt='facebook icon' width={24} height={24} /> </Link>
            <Link href="#"> <Image src="/icons/youtube-logo.svg" alt='youtube icon' width={24} height={24} /> </Link>
            <Link href="#"> <Image src="/icons/spotify-logo.svg" alt='whatsapp icon' width={24} height={24} /> </Link>
          </div>
        </div>
        <div className='flex items-start justify-end gap-21'>
          <div>
            <div>
              <h3 className='font-semibold text-2xl text-[#571B93]'>Let&apos;s journey together.</h3>
              <div className='text-[12px]'>Walk in faith in real time, 24/7.</div>
            </div>
            <div>
              <Link href="#" className='p-3 flex items-center gap-3'>
                <Image src="/icons/play-icon-primary.svg" width={24} height={24} alt='play icon' />
                <span>Listen now</span>
              </Link>
              <Link href="#" className='p-3 flex items-center gap-3'>
                <Image src="/icons/praying-hands.svg" width={24} height={24} alt='play icon' />
                <span>Meet Jesus</span>
              </Link>
            </div>
          </div>
          <div>
            <h3 className='text-[#571B93] font-semibold'>Getting Started</h3>
            <div className='flex flex-col gap-3 pt-4'>
              <Link href="#" className='text-sm'>Foundations of Christianity</Link>
              <Link href="#" className='text-sm'>Blog <div className='text-[12px] bg-[#3006534D] rounded-full p-0.5 px-1.5 inline-block'>New</div> </Link>
              <Link href="#" className='text-sm'>Good News</Link>
              <Link href="#" className='text-sm'>Faith FAQs</Link>
              <Link href="#" className='text-sm'>Stories of Transformation</Link>
            </div>
          </div>
          <div>
            <h3 className='text-[#571B93] font-semibold'>Faith-building</h3>
            <div className='flex flex-col gap-3 pt-4'>
              <Link href="#" className='text-sm'>Resources</Link>
              <Link href="#" className='text-sm'>Radio</Link>
              <Link href="#" className='text-sm'>Bible Studies</Link>
              <Link href="#" className='text-sm'>Daily Prayer</Link>
              <Link href="#" className='text-sm'>Know God</Link>
            </div>
          </div>
          <div>
            <h3 className='text-[#571B93] font-semibold'>Connect</h3>
            <div className='flex flex-col gap-3 pt-4'>
              <Link href="#" className='text-sm'>About Us</Link>
              <Link href="#" className='text-sm'>Chat with Sandra</Link>
              <Link href="#" className='text-sm'>Connect</Link>
              <Link href="#" className='text-sm'>Discipleship</Link>
              <Link href="#" className='text-sm'>What&apos;s Next</Link>
              <Link href="#" className='text-sm'>FAQs</Link>
            </div>
          </div>
        </div>
      </div>
      <div className='bg-gray-200 h-[1px] my-8' />
      <div className='text-sm flex items-center justify-between'>
        <div>&copy; 2025 E-mission to Islam. All rights reserved.</div>
        <div className='flex items-end gap-4'>
          <Link href='#'>Privacy Policy</Link>
          <Link href='#'>Terms and Conditions</Link>
        </div>
      </div>
    </footer>
  )
}
