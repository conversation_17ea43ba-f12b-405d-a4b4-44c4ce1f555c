{"extends": "../../typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"], "@repo/ui/*": ["../../packages/ui/*"], "@repo/utils/*": ["../../packages/utils/*"]}, "module": "esnext", "moduleResolution": "node", "esModuleInterop": true, "resolveJsonModule": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "next.config.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next.config.mjs"], "exclude": ["node_modules"]}