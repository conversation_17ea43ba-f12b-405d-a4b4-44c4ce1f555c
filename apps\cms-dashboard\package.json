{"name": "cms-dashboard", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@repo/ui": "*", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8.0.0", "typescript": "^5.0.0", "@types/jest": "^29.5.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.1.0", "eslint-config-next": "^14.0.0"}}