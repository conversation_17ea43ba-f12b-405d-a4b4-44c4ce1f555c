{"name": "gro-turbo-repo", "private": true, "scripts": {"build:frontend": "turbo run build --filter=frontend", "dev:frontend": "turbo run dev --filter=frontend", "build:api": "turbo run build --filter=api", "dev:api": "turbo run dev --filter=api", "build:dashboard": "turbo run build --filter=cms-dashboard", "dev:dashboard": "turbo run dev --filter=cms-dashboard", "dev": "turbo run dev", "start": "turbo run start", "lint": "turbo run lint --filter=frontend", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "clean": "sh ./scripts/npm-clean-install.sh"}, "devDependencies": {"@types/validator": "^13.15.2", "react": "^19.1.0", "react-dom": "^19.1.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "prettier": "^3.5.0", "tailwindcss": "^4.1.8", "turbo": "^2.4.2", "typescript": "5.7.3", "eslint": "^9.0.0", "eslint-config-next": "^15.3.3", "@types/eslint": "^9.0.0"}, "overrides": {"react": "^19.1.0", "react-dom": "^19.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.1.0", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@heroui/button": "^2.2.21", "@heroui/react": "^2.7.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "react": "^18.3.1", "react-dom": "^18.3.1"}}