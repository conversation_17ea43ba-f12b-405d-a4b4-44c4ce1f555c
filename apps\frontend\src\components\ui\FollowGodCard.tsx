"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { useState } from "react";
import SecondaryBtn from "./SecondaryBtn";

interface FollowGodProps {
    title: string;
    description: string;
    imageUrl: string;
    imageAlt: string;
}

const FollowGodCard = ({ title, description, imageUrl, imageAlt }: FollowGodProps) => {
    const [isHovered, setIsHovered] = useState(false);

    const cardVariants = {
        initial: { scaleY: 1 },
        hover: { scaleY: 0.9 }
      }
      
      const buttonVariants = {
        initial: { opacity: 0, y: 10 },
        hover: { opacity: 1, y: 0 }
      }

    return (

        <div>
            <motion.div
                whileHover={{ scale: 0.9 }}
                transition={{ duration: 0.003, ease: "easeInOut" }}
                style={{ originY: 0 }}
            >
                <div>
                <Image
                    src={imageUrl}
                    alt={imageAlt}
                    fill
                    className="h-[265px] object-cover rounded-xl transition-all duration-300"
                />
                </div>

            </motion.div>
       
        
        {/* <motion.div
            variants={cardVariants}
            initial="initial"
            whileHover="hover"
            transition={{ duration: 0.003, ease: "easeInOut" }}
            style={{ originY: 0 }}
            className="relative flex flex-col justify-start items-start h-[439px] lg:w-[384px] md:w-[208px] gap-4 bg-white rounded-xl shadow-lg p-2 transition overflow-hidden"
        >
            <motion.div
                
                className="absolute lg:w-[368px] rounded-xl overflow-hidden"
            >
                <Image
                    src={imageUrl}
                    alt={imageAlt}
                    fill
                    className="h-[265px] object-cover rounded-xl transition-all duration-300"
                />
            </motion.div>

            <div className="hidden group-hover:block w-19 h-9 ">
                    <h3 className="text-default-900 font-semibold text-base md:text-lg font-poppins">Hello Betty</h3>
                </div>
        </motion.div> */}
        </div>
    );

    //     <motion.div
    //         whileHover={{ scaleY: 1.02 }}
    //         transition={{ duration: 0.003, ease: "easeInOut" }}
    //         style={{ originY: 0 }}
    //         className="flex flex-col justify-start items-start lg:w-[384px] md:w-[208px] gap-4 bg-white rounded-xl shadow-lg p-2 transition overflow-hidden"
    //     >
    //         {/* Image that shrinks vertically on hover */}
    //         <motion.div
    //             initial={{ height: 265 }}
    //             animate={{ height: isHovered ? 220 : 265 }}
    //             transition={{ duration: 0.3 }}
    //             className="relative lg:w-[368px] rounded-xl overflow-hidden"
    //         >
    //             <Image
    //                 src={imageUrl}
    //                 alt={imageAlt}
    //                 fill
    //                 className="object-cover rounded-xl transition-all duration-300"
    //             />
    //         </motion.div>

    //         {/* Text that moves up slightly */}
    //         <motion.div
    //             animate={{ y: isHovered ? -10 : 0 }}
    //             transition={{ duration: 0.3 }}
    //             className="relative flex flex-col justify-start items-start gap-2 p-5"
    //         >
    //             <h3 className="text-default-900 font-semibold text-base md:text-lg font-poppins">
    //                 {title}
    //             </h3>
    //             <p className="text-default-600 text-sm font-normal font-poppins">
    //                 {description}
    //             </p>
    //         </motion.div>

    //         {/* Reserved space for the button – hidden unless hovered */}
    //         <div className="px-5 pb-4 h-[40px]">
    //             <motion.div
    //                 initial={{ opacity: 0, y: 10 }}
    //                 animate={{ opacity: isHovered ? 1 : 0, y: isHovered ? 0 : 10 }}
    //                 transition={{ duration: 0.3 }}
    //                 className="w-fit"
    //             >
    //                 <div className="justify-start text-colors-base-secondary text-sm font-semibold font-['Poppins'] leading-tight">
    //                         <SecondaryBtn text="Button" />
    //                 </div>
    //             </motion.div>
    //         </div>
    //     </motion.div>
    // );

//     <motion.div
//     variants={cardVariants}
//     initial="initial"
//     whileHover="hover"
//     transition={{ duration: 0.003, ease: "easeInOut" }}
//     style={{ originY: 0 }}
//     className="relative flex flex-col justify-start items-start h-[300px] lg:w-[384px] md:w-[208px] gap-4 bg-white rounded-xl shadow-lg p-2 transition overflow-hidden"
// >
//     {/* Your existing content */}
    
//             <motion.div
//         variants={buttonVariants}
//         initial="initial"
//         animate="initial"
//         className="absolute bottom-2 left-1/2 transform -translate-x-1/2"
//     >
//                 <SecondaryBtn text="Button" />

//             </motion.div>
//         </motion.div>
//     );
};

export default FollowGodCard;

// import React from "react";
// import Image from "next/image";
// import { motion } from "framer-motion";
// import SecondaryBtn from "./SecondaryBtn";

// interface FollowGodProps {
//     title: string;
//     description: string;
//     imageUrl: string;
//     imageAlt: string;
// }

// // const FollowGodCard = ({ title, description, imageUrl, imageAlt }: FollowGodProps) => {
// //     return (
// //       // Outer card that grows downward on hover
// //       <motion.div
// //         className="relative flex flex-col items-start bg-white rounded-xl shadow-lg w-80 p-4 overflow-hidden transition"
// //         initial="rest"
// //         whileHover="hover"
// //         animate="rest"
// //         variants={{
// //           rest: { height: "auto" },
// //           hover: { height: 340 }, // grow down on hover
// //         }}
// //       >
// //         {/* Inner content that shifts up on hover */}
// //         <motion.div
// //           variants={{
// //             rest: { y: 0 },
// //             hover: { y: -20 }, // move up on hover
// //           }}
// //           transition={{ type: "spring", stiffness: 300 }}
// //           className="flex flex-col gap-4"
// //         >
// //           <div className="relative w-full h-[145px] rounded-xl overflow-hidden">
// //             <Image
// //               src={imageUrl}
// //               alt={imageAlt}
// //               fill
// //               className="object-cover rounded-xl"
// //               sizes="(max-width: 768px) 100vw, 208px"
// //             />
// //           </div>
// //           <h3 className="text-default-900 font-semibold text-base md:text-lg font-poppins">
// //             {title}
// //           </h3>
// //           <p className="text-default-600 text-sm font-normal font-poppins">
// //             {description}
// //           </p>
// //         </motion.div>
  
// //         {/* Button appears in the gap when hovered */}
// //         <motion.div
// //           className="absolute bottom-4 left-4 right-4"
// //           variants={{
// //             rest: { opacity: 0, y: 20 },
// //             hover: { opacity: 1, y: 0 },
// //           }}
// //           transition={{ duration: 0.3 }}
// //         >
// //           <div className="rounded-[50px] flex justify-center items-center gap-1.5 overflow-hidden">
// //                 <div className="justify-start text-colors-base-secondary text-sm font-semibold font-['Poppins'] leading-tight">
// //                     <SecondaryBtn text="Learn More" />
// //                 </div>
// //                 <div data-size="12" className="w-3 h-3 relative overflow-hidden">
// //                     <div className="w-1.5 h-2.5 left-[3.50px] top-[0.75px] absolute bg-colors-base-secondary" />
// //                 </div>
// //             </div>
// //         </motion.div>
// //       </motion.div>
// //     );

// const FollowGodCard = ({ title, description, imageUrl, imageAlt }: FollowGodProps) => {
//     return (
//         // Outer card that grows downward on hover
//         <motion.div 
//         initial={{ height: 425 }}
//         whileHover={{ height: 435 }} // slightly expand card height on hover
//         transition={{ duration: 0.3 }}

//         className="flex flex-col justify-start items-start lg:w-[384px] md:w-[208px] gap-4 bg-white rounded-xl shadow-lg p-2 transition hover:shadow-2xl">
//             <motion.div 
//             initial={{ height: 265 }}
//             whileHover={{ height: 220 }} // lift up content on hover
//             transition={{ duration: 0.09 }}
//             className="relative lg:w-[368px] rounded-xl overflow-hidden">
//                 <Image
//                     src={imageUrl}
//                     alt={imageAlt}
//                     fill
//                     className="object-cover rounded-xl"
//                 />
//             </motion.div>


//             <div className="relative flex flex-col justify-start items-start gap-2 p-5">
//                 <h3 className="text-default-900 font-semibold text-base md:text-lg font-poppins">
//                     {title}
//                 </h3>
//                 <p className="text-default-600 text-sm font-normal font-poppins">
//                     {description}
//                 </p>
//             </div>
//         </motion.div>
//     );
// }
    
// // const FollowGodCard = ({ title, description, imageUrl, imageAlt }: FollowGodProps) => {
// //     return (
// //         // Outer motion.div wraps the entire card and tracks the hover state
// //         <motion.div
// //             initial="rest"
// //             whileHover="hover"
// //             animate="rest"
// //             variants={{
// //                 rest: { height: 425 },
// //                 hover: { height: 435 }, // slightly expand card height on hover
// //             }}
// //             className="relative flex flex-col justify-start items-start lg:w-[384px] md:w-[208px] gap-4 bg-white rounded-xl shadow-lg p-2 transition-all duration-300 ease-in-out overflow-hidden"
// //         >
// //             {/* This inner motion.div lifts image + text up slightly on hover */}
// //             <motion.div
// //                 variants={{
                    
// //                 }}
// //                 className="flex flex-col gap-4"
// //             >
// //                 <div className="relative lg:w-[368px] h-[265px] rounded-xl overflow-hidden">
// //                     <Image
// //                         src={imageUrl}
// //                         alt={imageAlt}
// //                         fill
// //                         className="object-cover rounded-xl"
// //                     />
// //                 </div>
// //                 <div className="relative flex flex-col justify-start items-start gap-2 px-5">
// //                     <h3 className="text-default-900 font-semibold text-base md:text-lg font-poppins">
// //                         {title}
// //                     </h3>
// //                     <p className="text-default-600 text-sm font-normal font-poppins">
// //                         {description}
// //                     </p>
// //                 </div>
// //             </motion.div>

// //             {/* Button appears on hover: fades and moves up into view */}
// //             <motion.button
// //                 variants={{
// //                     rest: { opacity: 0, y: 20, pointerEvents: "none" },
// //                     hover: { opacity: 1, y: 0, pointerEvents: "auto" },
// //                 }}
// //                 transition={{ duration: 0.3 }}
// //                 className="ml-5 mt-2 px-4 py-2 bg-blue-600 text-white rounded-md shadow hover:bg-blue-700"
// //             >
// //                 Learn More
// //             </motion.button>
// //         </motion.div>
// //     );
// // };

// export default FollowGodCard;
// //end