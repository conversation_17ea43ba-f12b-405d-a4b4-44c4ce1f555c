"use client";

import FollowGodCard from "./ui/FollowGodCard";
import { useDictionary } from '@/lib/DictionaryContext';


const MoreWaysToFollowGod = () => {
    const dictionary = useDictionary();
    return (
        <div className="px-28 py-16 flex justify-center"/*className="w-full bg-white px-4 md:px-12 lg:px-28 py-16 self-stretch"*/>
            <div className="flex flex-col gap-10">
                <h2 className="self-stretch justify-start text-default-900 text-5xl font-semibold text-center font-['Poppins'] leading-[60px]">
                    {dictionary.FollowGod.title}
                </h2>
                <div className="flex flex-wrap justify-start gap-6">
                    {dictionary.FollowGod.cards.map((card) => (
                        <FollowGodCard
                            key={card.id}
                            title={card.title}
                            description={card.description}
                            imageUrl={card.imageUrl}
                            imageAlt={card.imageAlt}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
}

export default MoreWaysToFollowGod;