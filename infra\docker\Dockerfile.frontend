# Use official Node image as base
FROM node:18-alpine AS base
RUN apk add --no-cache libc6-compat
WORKDIR /app

# ---- Builder stage -----
FROM base AS builder

# Expose environment variables
#ARG NEXT_PUBLIC_STATS_API_URL
#ENV NEXT_PUBLIC_STATS_API_URL=$NEXT_PUBLIC_STATS_API_URL

COPY . .
RUN npm install -g turbo
RUN turbo prune frontend --docker


# ----- Installer stage ----
FROM base AS installer
WORKDIR /app

# Set environment variables
#ARG NEXT_PUBLIC_STATS_API_URL
#ENV NEXT_PUBLIC_STATS_API_URL=$NEXT_PUBLIC_STATS_API_URL
#ENV NODE_ENV=production

# Copy only necessary files to install the dependencies
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/package-lock.json ./package-lock.json
COPY --from=builder /app/.gitignore .gitignore

# Enable Corepack to manage Yarn version specified in package.json
RUN corepack enable

# Install pruned dependencies
# --legacy-peer-deps is needed to avoid peer dependency issues with turbo
RUN npm ci --legacy-peer-deps

# Copy full source for build (including packages)
COPY --from=builder /app/out/full/ .
COPY turbo.json turbo.json
COPY --from=builder /app/packages/typescript-config /app/packages/typescript-config
COPY --from=builder /app/typescript-config /app/typescript-config

# Run build for frontend only
RUN npx turbo build --filter=frontend...

# ---- Production Stage -----
FROM base AS runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs
USER nextjs

# Copy only necessary production files 
COPY --from=installer /app/apps/frontend/next.config.ts .
COPY --from=installer /app/apps/frontend/package.json .

# Standalone build output
COPY --from=installer --chown=nextjs:nodejs /app/apps/frontend/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/frontend/.next/static ./apps/frontend/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/frontend/public ./apps/frontend/public

# Default port
EXPOSE 3000

CMD ["node", "apps/frontend/server.js"]