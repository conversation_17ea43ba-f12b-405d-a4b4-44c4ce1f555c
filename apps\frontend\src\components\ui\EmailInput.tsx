import { ChangeEvent } from 'react';

interface EmailInputProps {
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  invalidEmail?: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
}

export default function EmailInput({
  value,
  onChange,
  invalidEmail,
  label = 'Email',
  placeholder = '<EMAIL>',
  required = true,
  className = '',
}: EmailInputProps) {

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <div className="flex items-center gap-1">
          <label 
            htmlFor="email-input"
            className="block text-gray-900 text-sm font-medium font-['Poppins']"
          >
            {label}
          </label>
          {required && <span className="text-red-500">*</span>}
        </div>
      )}
      <input
        id="email-input"
        type="email"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={`w-full px-4 py-2 bg-white rounded-md border ${
          invalidEmail ? 'border-red-500' : 'border-gray-300'
        } focus:outline-none focus:border-[#571B93] transition-colors text-gray-900 font-['Poppins']`}
        aria-label="Input your email address"
      />
      {invalidEmail && (
        <div className="flex items-center gap-2 mt-1">
          <div className="w-4 h-4 rounded-full bg-red-500 text-white flex items-center justify-center text-xs">!</div>
          <p className="text-red-500 text-sm">{invalidEmail}</p>
        </div>
      )}
    </div>
  );
}