/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: { // You can name your color and set its value here
        layout: {
          background: "#ffffff",
          foreground: "#11181c",
          divider: "#dcdcdc",
          focus: "#006fee",
        },
        content: {
          1: "#ffffff",
          2: "#f4f4f5",
          3: "#e4e4e7",
          4: "#d4d4d8",
        },
        default: {
          50: "#fafafa",
          100: "#f4f4f5",
          200: "#e4e4e7",
          300: "#d4d4d8",
          400: "#a1a1aa",
          500: "#71717a",
          600: "#52525b",
          700: "#3f3f46",
          800: "#27272a",
          900: "#18181b",
        },
        primary: {
          50: "#e6f1fe",
          100: "#cce3fd",
          200: "#99c7fb",
          300: "#66aaf9",
          400: "#338ef7",
          500: "#006fee",
          600: "#005bc4",
          700: "#004493",
          800: "#002e62",
          900: "#001731",
        },
        secondary: {
          50: "#f2eafa",
          100: "#e4d4f4",
          200: "#c9a9e9",
          300: "#ae7ede",
          400: "#9353d3",
          500: "#7828c8",
          600: "#6020a0",
          700: "#481878",
          800: "#301050",
          900: "#180828",
        },
        success: {
          50: "#e8faf0",
          100: "#d1f4e0",
          200: "#a2e9c1",
          300: "#74dfa2",
          400: "#45d483",
          500: "#17c964",
          600: "#12a150",
          700: "#0e793c",
          800: "#095028",
          900: "#032815",
        },
        warning: {
          50: "#fefce8",
          100: "#fdedd3",
          200: "#fbdba7",
          300: "#f9c97c",
          400: "#f7b750",
          500: "#f5a524",
          600: "#c4841d",
          700: "#936316",
          800: "#62420e",
          900: "#312107",
        },
        danger: {
          50: "#fee7ef",
          100: "#fdd0df",
          200: "#faa0bf",
          300: "#f871a0",
          400: "#f54180",
          500: "#f31260",
          600: "#c20e4d",
          700: "#920b3a",
          800: "#610726",
          900: "#310413",
        },
      },
      fontFamily: {
        poppins: ["Poppins", "sans-serif"],
      },
      fontSize: {
        // Body text styles
        'text-xs': ['12px', { lineHeight: '16px', fontWeight: '400' }],
        'text-sm': ['14px', { lineHeight: '20px', fontWeight: '400' }],
        'text-base': ['16px', { lineHeight: '24px', fontWeight: '400' }],
        'text-lg': ['18px', { lineHeight: '28px', fontWeight: '400' }],

        // Label styles
        'label-lg': ['18px', { lineHeight: '28px', fontWeight: '600' }],
        'label-base': ['16px', { lineHeight: '24px', fontWeight: '600' }],
        'label-sm': ['14px', { lineHeight: '20px', fontWeight: '500' }],
        'label-xs': ['12px', { lineHeight: '16px', fontWeight: '500' }],

        // Button styles
        'button-lg': ['18px', { lineHeight: '24px', fontWeight: '600' }],
        'button': ['14px', { lineHeight: '20px', fontWeight: '600' }],
      },
    },
  },
  plugins: [],
};

export default config;