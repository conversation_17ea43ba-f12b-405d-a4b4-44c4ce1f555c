---
name: 📌 API Task
about: Create, update, or fix an API route
title: "[API] <API Task Title>"
labels: api
assignees: ''

---

## 🌐 API Route
`/api/<route>`

## 📝 Task Description
<!-- Describe what needs to be done -->

## 🛠️ Implementation Steps
- [ ] Define route handler
- [ ] Implement middleware
- [ ] Write unit tests

## 🔗 Related Schemas
<!-- Mention related MongoDB schemas or database models -->

## 🚀 Additional Notes
<!-- Any extra details? -->
9