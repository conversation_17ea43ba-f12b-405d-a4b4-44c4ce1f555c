
## GRO EMission Backend

Built with [Nest](https://github.com/nestjs/nest) framework (TypeScript support)

### Run project 

```bash
$ npm install
$ npx turbo run dev
```

### Run tests

```bash
# unit tests
$ npm run test
```

## Deployment

Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

### Resources

Visit the [NestJS Documentation](https://docs.nestjs.com) for reference

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
