"use client";
import { Button } from "@heroui/button";
import Image from "next/image";
import Link from "next/link";
import { JSX, useState } from "react";
import ListenBtn from "./ui/ListenBtn";
import { ChevronDown, ChevronRight } from "lucide-react";

interface FAQItem {
  id: number;
  question: string;
  answer: JSX.Element;
}

export default function Faq() {
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const faqData: FAQItem[] = [
    {
      id: 1,
      question: "How does Christianity differ from Islam and other religions?",
      answer: (
        <>
          Christianity and Islam share some core beliefs; however, Christians
          embrace and see Jesus as God, while Islam adheres to a strict
          monotheism and views Jesus as a prophet.
        </>
      ),
    },
    {
      id: 2,
      question: "Do Christians worship three gods?",
      answer: (
        <>
          No, Christianity teaches that there is only one God, who reveals
          Himself as Father, Son (Jesus), and the Holy Spirit.
        </>
      ),
    },
    {
      id: 3,
      question: "Do Christians believe in the same prophets as Muslims?",
      answer: (
        <>
          Yes, Christians believe in prophets like <PERSON>, <PERSON>, <PERSON>, <PERSON>,
          <PERSON>, <PERSON>, and <PERSON> the Baptist. However, Jesus is believed to be
          more than a prophet—He is the Word of God (<PERSON><PERSON><PERSON><PERSON>), sent not
          only to teach but to bring salvation.
        </>
      ),
    },
    {
      id: 4,
      question:
        "What do Christians believe about paradise (Jannah) and judgment?",
      answer: (
        <>
          Christians believe in salvation through eternal life and a final
          judgment where every person will stand before God. Salvation is a gift
          from God, not earned by doing good deeds.
        </>
      ),
    },
    {
      id: 5,
      question:
        "I am a Muslim, but I want to learn more about Jesus. What should I do?",
      answer: (
        <>
          You are welcome to explore at your own pace! Read the{" "}
          <span className="underline">Injil</span>, starting with John, and pray
          for guidance. You can also{" "}
          <span className="underline">connect with one of the mentors</span> and
          ask about their journey.
        </>
      ),
    },
    {
      id: 6,
      question: "What is Christianity, and what do Christians believe?",
      answer: (
        <>
          Christianity is a monotheistic religion centered around the life,
          teachings, death, and resurrection of Jesus Christ. Christians believe
          in one God who exists as a Trinity—Father, Son (Jesus Christ), and
          Holy Spirit. They affirm Jesus as the Son of God and the savior of
          humanity, who offers forgiveness of sins and eternal life to those who
          believe in Him.
        </>
      ),
    },
  ];

  const toggleItem = (id: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  const isOpen = (id: number) => openItems.has(id);

  return (
    <div
      data-layout="Dropdown"
      className="self-stretch px-28 py-20 inline-flex flex-col justify-start items-center gap-16 overflow-hidden"
    >
      <div className="self-stretch inline-flex justify-center items-start gap-24">
        <div className="w-96 inline-flex flex-col justify-start items-start gap-6">
          <div
            data-heading-size="H2"
            data-show-body="true"
            data-show-subtitle="false"
            className="w-full max-w-[750px] min-w-72 flex flex-col justify-start items-start gap-6"
          >
            <div className="self-stretch flex flex-col justify-start items-start gap-3">
              <div className="self-stretch justify-start text-colors-base-default-900 text-5xl font-semibold font-['Poppins'] leading-[60px]">
                Frequently asked questions
              </div>
            </div>
            <div className="self-stretch justify-center text-colors-base-default-600 text-base font-normal font-['Poppins'] leading-normal">
              Curious about Jesus? Have you ever wondered about the Bible? Here
              are some of our most asked questions from seekers like you.
            </div>
          </div>
          <div
            data-show-leading-icon="false"
            data-show-trailing-icon="false"
            data-state="Default"
            data-text-size="text-sm"
            data-variant="Solid"
            className="min-w-24 px-5 py-1.5 bg-violet-900 rounded-[50px] inline-flex justify-center items-center gap-1.5 overflow-hidden hover:bg-violet-500 transition-colors duration-200 cursor-pointer"
          >
            <div className="justify-start text-white text-sm font-semibold font-['Poppins'] leading-tight">
              See all questions
            </div>
          </div>
        </div>
        <div className="flex-1 max-w-[1280px] inline-flex flex-col justify-start items-center">
          <div className="w-full max-w-[920px] flex flex-col justify-start items-start">
            {faqData.map((item, index) => (
              <div
                key={item.id}
                data-state={isOpen(item.id) ? "Open" : "Default"}
                className={`self-stretch min-w-80 px-6 py-4 ${index === 0 ? "border-t" : ""} border-b border-colors-base-default-300 flex flex-col justify-start items-center hover:bg-gray-100 ${isOpen(item.id) ? "bg-gray-100" : ""} transition-colors cursor-pointer`}
                onClick={() => toggleItem(item.id)}
              >
                <div className="self-stretch flex flex-col justify-start items-start gap-4">
                  <div className="self-stretch inline-flex justify-between items-center">
                    <div className="flex-1 justify-start text-colors-base-default-900 text-xl font-semibold font-['Poppins'] leading-7">
                      {item.question}
                    </div>
                    <div
                      data-size="24"
                      className="w-6 h-6 relative overflow-hidden"
                    >
                      {isOpen(item.id) ? (
                        <ChevronDown className="w-5 h-5 text-colors-base-default-500 transition-transform duration-200" />
                      ) : (
                        <ChevronRight className="w-5 h-5 text-colors-base-default-500 transition-transform duration-200" />
                      )}
                    </div>
                  </div>
                  {isOpen(item.id) && (
                    <div className="self-stretch pt-2 pb-2 transition-all duration-300 ease-in-out">
                      <div className="text-colors-base-default-600 text-base font-normal font-['Poppins'] leading-normal">
                        {item.answer}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
