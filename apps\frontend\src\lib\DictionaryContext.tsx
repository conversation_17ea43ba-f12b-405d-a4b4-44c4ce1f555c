"use client";
import React, { createContext, ReactNode, useContext } from "react";
import dictionary from "../dictionary.json";

type Dictionary = typeof dictionary;

type ContentProviderProps = {
  children: ReactNode;
  content: Dictionary;
};

const ContentContext = createContext<Dictionary>(dictionary);

export function ContentProvider({ children, content }: ContentProviderProps) {
  return <ContentContext.Provider value={content}>{children}</ContentContext.Provider>;
}

export function useDictionary(): Dictionary {
  const context = useContext(ContentContext);
  if (Object.keys(context).length === 0) {
    throw new Error("Dictionary content is empty. Provide valid content.");
  }
  return context;
}
