import { Button } from '@heroui/button';
import React from 'react';
import { motion } from 'framer-motion';

interface PrimaryBtnProps {
  text: string;
  link?: string;
  onClick?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

export default function PrimaryBtn(PrimaryBtnProps: PrimaryBtnProps) {
  return (
      <motion.div
        whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
      className="inline-block origin-center"
    >
      {/* min-w-24 px-6 py-2 bg-violet-900 rounded-[50px] inline-flex justify-center items-center gap-2.5 overflow-hidden hover:bg-violet-800 transition-colors duration-300 */}
        <Button 
        className="bg-violet-800 text-white rounded-full px-6 py-2 font-['Poppins'] font-semibold text-sm hover:bg-violet-700 transition-colors duration-300 cursor-pointer"
        href={PrimaryBtnProps.link}
        onClick={PrimaryBtnProps.onClick}
        >
          {PrimaryBtnProps.text}
        </Button>
      
      </motion.div>
  );
}
