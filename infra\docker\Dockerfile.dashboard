#ARG NODE_VERSION=18.17.0

FROM node:18-alpine As base

FROM base AS builder
RUN apk add --no-cache libc6-compat
RUN apk update

# Set working directory
WORKDIR /app

# Install Turbo globally
RUN npm install -g turbo

COPY . .
RUN turbo prune cms-dashboard --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
RUN apk add --no-cache libc6-compat
RUN apk update
WORKDIR /app

# Install the dependencies
COPY .gitignore .gitignore
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/package-lock.json ./package-lock.json

# Enable Corepack to manage Yarn version specified in package.json
RUN corepack enable
RUN npm ci --legacy-peer-deps

# Build the project
COPY --from=builder /app/out/full/ .
#COPY --from=builder /app/packages /app/packages
COPY --from=builder /app/packages/typescript-config/nextjs.json /app/packages/typescript-config/nextjs.json
COPY turbo.json turbo.json

# Run turbo build (filtered)
RUN npx turbo build --filter=cms-dashboard...

FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

COPY --from=installer /app/apps/cms-dashboard/next.config.ts .
COPY --from=installer /app/apps/cms-dashboard/package.json .

# Reduce image size
COPY --from=installer --chown=nextjs:nodejs /app/apps/cms-dashboard/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/cms-dashboard/.next/static ./apps/cms-dashboard/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/cms-dashboard/public ./apps/cms-dashboard/public

CMD node apps/cms-dashboard/server.js