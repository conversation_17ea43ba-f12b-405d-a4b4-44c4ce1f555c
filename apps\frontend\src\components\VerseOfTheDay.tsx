"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@heroui/react";
import { popularVerses } from "../models/BibleVerse";
import { ArrowRight } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

export default function VerseOfTheDay() {
  const [showAll, setShowAll] = useState(false);

  const mainVerse = popularVerses[0];
  const featuredVerses = showAll
    ? popularVerses.slice(1)
    : popularVerses.slice(1, 4);

  if (!mainVerse) return null;

  return (
    <section className="w-full bg-white px-6 md:px-16 py-24">
      {/* Verse of the Day */}
      <div className="flex flex-col lg:flex-row items-start gap-10">
        <div
          className="w-full lg:w-[350px] h-[350px] rounded-xl bg-cover bg-center"
          style={{ backgroundImage: "url('/images/verse-pic.png')" }}
        />
        <div className="flex-1 flex flex-col gap-4">
          <h4 className="text-purple-700 font-semibold text-base md:text-lg font-poppins">
            Verse of the Day
          </h4>
          <h2 className="text-3xl md:text-5xl font-bold text-gray-900 font-poppins">
            God’s Ultimate Love and Promise
          </h2>
          <p className="text-gray-600 text-base md:text-lg font-medium font-poppins">
            {mainVerse.book} {mainVerse.chapter}:{mainVerse.verse} (KJV)
          </p>
          <p className="text-gray-800 text-base font-poppins leading-relaxed">
            {mainVerse.text}
          </p>
          <Button className="mt-4 w-fit px-6 py-2 rounded-full bg-purple-700 hover:bg-purple-800 text-white text-sm font-semibold font-poppins">
            Read full chapter
          </Button>
        </div>
      </div>

      {/* Featured Verses */}
      <div className="mt-24">
        <div className="flex justify-between items-center mb-8">
          <h3 className="text-3xl md:text-4xl font-bold font-poppins">
            Featured Verses
          </h3>
          <button
            onClick={() => setShowAll((prev) => !prev)}
            className="text-sm text-gray-900 font-semibold flex items-center gap-1 hover:underline"
          >
            {showAll ? "Show less" : "Show all"}
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          <AnimatePresence>
            {featuredVerses.map((v, index) => (
              <motion.div
                key={v.book + v.chapter + v.verse}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
              >
                <div className="w-10 h-2 bg-purple-700 mb-4"></div>
                <h4 className="text-lg font-semibold font-poppins mb-2">
                  {v.book} {v.chapter}:{v.verse} (KJV)
                </h4>
                <p className="text-gray-700 text-sm font-poppins">{v.text}</p>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </div>
    </section>
  );
}
