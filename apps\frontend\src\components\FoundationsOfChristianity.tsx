"use client";

import { useState } from "react";
import Image from "next/image";
import PrimaryBtn from "./ui/PrimaryBtn";

export default function Foundations() {
  // If you want to use the video functionality, uncomment and use setShowVideo
  // const [showVideo, setShowVideo] = useState(false);

  return (
    <>
      {/* Hero Section */}
      <div className="relative w-full h-[320px] md:h-[480px] lg:h-[650px] flex flex-col justify-center items-center overflow-hidden -mt-[15px]">
        <div className="absolute inset-0 w-full h-full" style={{ top: "-25px", bottom: "-135px", height: "93.5%" }}>
          <Image
            src="/images/foundations/foundations-banner.png"
            alt="Foundations of Christianity Banner"
            fill
            priority
            className="object-cover object-center"
            style={{ filter: "brightness(0.6) saturate(0.95)" }}
            sizes="100vw"
            quality={90}
          />
        </div>
        <div className="relative z-10 flex flex-col items-center justify-center h-full w-full">
          <div
            className="max-w-2xl text-2xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold text-center text-white font-['Poppins'] translate-x-[50px]"
            style={{ position: "relative", top: "-59px", fontSize: "3.85em" }} // moved up 15px
          >
            <span className="block">
              Foundations of
            </span>
            <span className="block mt-3">
              Christianity
            </span>
          </div>
          <div
            className="max-w-2xl text-center text-white text-xs md:text-sm lg:text-base font-normal font-['Poppins'] translate-x-[50px]"
            style={{ position: "relative", top: "-29px", fontSize: "1.1em" }} // moved up 15px
          >
            Deepen your understanding of core Christian beliefs
          </div>
        </div>
      </div>

      {/* Main Content Section */}
      <div className="w-full bg-white flex items-center py-10 md:py-16 lg:py-20 -mt-[68px]">
        <div className="flex flex-col lg:flex-row w-full max-w-screen-xl mx-auto px-4 md:px-12 gap-8 lg:gap-16">
          {/* Text Content */}
          <div className="flex-1 min-w-0 rounded-2xl flex flex-col justify-start items-start gap-4">
            <div
              data-heading-size="H2"
              data-show-body="true"
              data-show-subtitle="true"
              className="w-full flex flex-col justify-start items-start gap-8 translate-x-[60px]"
            >
                <div
                  className="flex flex-col justify-start items-start gap-6"
                  style={{ marginRight: "calc(60px - 20px)" }} // 60px (original translate-x) - 20px
                >
                  <div className="text-[#7828c8] text-base md:text-lg lg:text-xl font-semibold font-['Poppins'] leading-relaxed tracking-tight">
                    Foundations of Christianity
                  </div>
                  <div
                    className="text-gray-800 text-2xl md:text-4xl lg:text-5xl font-semibold font-['Poppins'] tracking-tight leading-relaxed -translate-y-[9px]"
                    style={{ lineHeight: "1.26", display: "flex", flexDirection: "column" }}
                  >
                  <span className="block mb-[8px]">What does it take to start a relationship with God?</span>
                  </div>
                </div>
                <div className="text-gray-600 text-xs md:text-sm lg:text-base font-normal font-['Poppins'] tracking-normal -translate-y-[25px]" style={{ marginRight: "calc(60px - 20px)" }}>
                  Is it about committing to selfless acts or striving to become a better person to gain His acceptance?
                </div>
                <div className="text-gray-600 text-xs md:text-sm lg:text-base font-normal font-['Poppins'] tracking-normal -translate-y-[40px]" style={{ marginRight: "calc(80px - 20px)" }}>
                  Surprisingly, none of these efforts are the key. The Bible clearly outlines how we can truly know Him. The following truths reveal how you can personally begin a relationship with God through Jesus Christ, starting today.
                </div>

            </div>
          </div>
          {/* Image/Video Content */}
          <div className=" relative flex-1 min-w-0 bg-black/10 rounded-3xl flex flex-col justify-center items-center overflow-hidden aspect-video max-h-[408px] lg:max-h-[510px]">

              <Image
                src="/images/foundations/cross-hands.png"
                alt="Cross and Hands"
                fill
                style={{ filter: "brightness(0.88)" }}
                className="object-cover rounded-3xl"
                sizes="(max-width: 1024px) 100vw, 50vw"
              />
              <button
                type="button"
                className="absolute left-1/2 top-1/2 z-20 -translate-x-1/2 -translate-y-1/2 transition-all duration-200 hover:scale-90 group active:brightness-75"
                style={{ outline: "none" }}
                aria-label="Play Video"
              >
                <span className="block">
                  <Image
                    src="/images/foundations/play-button.png"
                    alt="Play"
                    width={96}
                    height={96}
                    className="transition-all duration-200 group-hover:brightness-125 group-active:brightness-75"
                    draggable={false}
                    priority
                  />
                </span>
              </button>
              {/* Video overlay can be added here */}
            </div>
          </div>

      </div>
    </>
  );
}