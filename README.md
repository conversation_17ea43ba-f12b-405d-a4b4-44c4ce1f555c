# GRO E-Mission to Islam

### Spreading the Gospel to Muslims for Christ and spiritual growth through digital missions. 

Welcome! This is a mono repository setup using [Turborepo](https://turbo.build/repo/docs)

This project includes:

- **📊 CMS Dashboard:** (`apps/cms-dashboard`) - This is a Next.js app
- **🖥️ Frontend:** (`apps/frontend`) - This is a Next.js app
- **⚡ Backend Api:** (`apps/api`) - This is a NestJS app
- **🚀 TurboRepo:** Managing everything efficiently


### 📂 Project Structure

```
gro-turbo-repo
│── apps
│   ├── cms-dashboard   # Admin dashboard
|   ├── frontend        # Mission website(s)
│   ├── api             # Backend API
│── packages            # Shared utilities/libraries
|   ├──ui-components    #Shared UI Library
|   ├──utils            #Reusable functions
│── turbo.json          # TurboRepo config
│── package.json        # Root package.json with workspace configs
│── README.md      # You're here! 🚀
```

## 🚀 Getting Started

### 1️⃣ Clone the Repo  
```sh
git clone https://github.com/GRO-Emissiontoislam/gro-cms-turbo-repo.git
cd gro-turbo-repo
```


### 2️⃣ Install Dependencies
```sh
npm install
```

### 3️⃣ Start Development
At root folder, run your prefered app using script shortcuts:

1. Admin dashboard (cms-dashboard)
```sh
npm run dev:dashboard
```

2. Mission Website (frontend)
```sh
npm run dev:frontend
```

3. Backend API (api)
```
npm run dev:api
```

### Or start them separately in app dir:
```sh
cd apps/cms-dashboard && npm run dev  # Start cms-dashboard app
cd apps/frontend && npm run dev  # Start frontend app
cd apps/api && npx turbo run dev  # Start backend-api
```

### All apps at once (Use this command when necessary)
```sh
turbo run dev
```

## 🚀 Apps ports:
- **cms-dashboard:** `-p 3000`
- **api:** `-p 3001`
- **frontend:** `-p 3002`
  

## 🆕 Add New App to Turbo Repo
```sh
# Create new Next.js app in apps folder
npx create-next-app@latest apps/my-new-app

# Install dependencies
cd apps/my-new-app
npm install
```


## 🛠 Tech Stack

- **Frontend:** [Next.js](https://nextjs.org/) (React, TypeScript)
- **Backend:** [NestJS](https://nestjs.com/) (TypeScript, Express)
- **Package Management:** npm
- **Monorepo Tooling:** [TurboRepo](https://turbo.build/)

## 📌 Environment Variables
Create a `.env` file in **both `apps/cms-dashboard/`, `apps/frontend/` and `apps/api/`** and add necessary variables.

Example `.env` for **api**:
```sh
PORT=3001
DATABASE_URL=your_database_url
JWT_SECRET=your_secret_key
```

Example `.env` for **frontend**:
```sh
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## 🏗️ DevOps 

### Local development
We use docker-compose to buid the app into docker images which are the deployable units.
We now separate the commads which can be run at once. First build the image and then run it. 

1. frontend
```sh
docker compose build gro-emission-frontend
```
```sh
docker compose up -d gro-emission-frontend
```

## 🚀 Deployment

- Hybrid (cloud& selfhosting)

## 🤝 Contributing

Want to contribute? Open an issue or submit a pull request!



🔹 **Happy Coding!** 🎉