import Footer from '@/components/Footer';
import MoreWaysToFollowGod from '@/components/MoreWaysToFollowGod';
import Navbar from '@/components/Navbar'
import Foundations from '@/components/FoundationsOfChristianity';
import MeetGod from '@/components/MeetGod';

import { footerConfig } from '@/lib/footerConfig';

const FoundationsOfChristianity = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      {/* TODO: ADD COMPONENT - Foundations of Christianity */}
      <Foundations />
    
      {/* TODO: ADD COMPONENT - Meet God */}
        <MeetGod />

      {/* TODO: ADD COMPONENT - Navigating Life's Challenges */}

      {/* TODO: ADD COMPONENT - Repentance [Banner Image] */}

      {/* TODO: ADD COMPONENT - More ways to follow God */}
      <MoreWaysToFollowGod />

      {/* TODO: ADD COMPONENT - Latest Articles */}

      <div className="flex-1"></div>
      <Footer {...footerConfig} />
    </div>
    );
};
    
export default FoundationsOfChristianity;