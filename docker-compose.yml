services:
  # Database
  gro-mongodb:
    container_name: gro-mongodb
    image: mongo:latest
    env_file:
      - .env
    restart: always
    ports:
      - '27017:27017'
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Backend API
  gro-emission-api:
    container_name: gro-emission-api
    build:
      context: .
      dockerfile: ./infra/docker/Dockerfile.api
      args:
        - NEXT_PUBLIC_STATS_API_URL=${NEXT_PUBLIC_STATS_API_URL}
    image: gro-emission-api
    env_file:
      - .env
    restart: always
    ports:
      - '3001:3001'
    depends_on:
      gro-mongodb:
        condition: service_healthy

# Frontend Frontend
  gro-emission-frontend:
    container_name: gro-emission-frontend
    build:
      context: .
      dockerfile: ./infra/docker/Dockerfile.frontend
    image: gro-emission-frontend
    env_file:
      - .env
    restart: always
    ports:
      - '3002:3002'
    # depends_on:
    #   gro-mongodb:
    #     condition: service_healthy